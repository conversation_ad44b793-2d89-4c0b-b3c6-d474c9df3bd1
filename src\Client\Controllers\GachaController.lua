--[[
	GachaController.lua - 抽卡控制器
	抽卡 UI、動畫、展示結果
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local Fusion = require(game:GetService("ReplicatedStorage").Packages.fusion)

-- Fusion 組件
local New = Fusion.New
local Children = Fusion.Children
local OnEvent = Fusion.OnEvent
local Value = Fusion.Value
local Computed = Fusion.Computed

-- 資料模組
local PetDatabase = require(game:GetService("ReplicatedStorage").Shared.Modules.PetDatabase)
local WeaponDatabase = require(game:GetService("ReplicatedStorage").Shared.Modules.WeaponDatabase)
local i18n = require(game:GetService("ReplicatedStorage").Shared.i18n)

local GachaController = Knit.CreateController({
	Name = "GachaController",
})

-- 私有變量
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- 狀態
local isGachaUIVisible = Value(false)
local gachaHistory = Value({})
local gachaPity = Value({})
local gachaCosts = Value({})
local isAnimating = Value(false)
local currentResults = Value({})

-- UI 引用
local gachaUI = nil

function GachaController:KnitStart()
	print("🎰 GachaController started")
	
	-- 獲取服務
	self.GachaService = Knit.GetService("GachaService")
	self.UIController = Knit.GetController("UIController")
	
	-- 監聽服務端事件
	self:_connectToServices()
	
	-- 等待 Knit 完全啟動後請求抽卡數據
	Knit.OnStart():andThen(function()
		self.GachaService.GetGachaData:Fire()
	end)
	
	-- 創建抽卡 UI
	self:_createGachaUI()
end

function GachaController:KnitInit()
	-- 初始化時不需要其他控制器
end

-- 連接到服務端服務
function GachaController:_connectToServices()
	-- 監聽抽卡數據更新事件
	self.GachaService.GachaDataUpdated:Connect(function(data)
		gachaHistory:set(data.history or {})
		gachaPity:set(data.pity or {})
		gachaCosts:set(data.costs or {})
		print("🎰 Received gacha data")
	end)
	
	-- 監聽抽卡結果
	self.GachaService.GachaResult:Connect(function(results, poolType, isTenPull)
		self:_handleGachaResult(results, poolType, isTenPull)
	end)
	
	-- 監聽金幣不足
	self.GachaService.InsufficientFunds:Connect(function(required, current)
		if self.UIController then
			self.UIController:ShowNotification(
				i18n.t("gacha.insufficient_coins"),
				3,
				Color3.fromRGB(255, 100, 100)
			)
		end
	end)
end

-- 創建抽卡 UI
function GachaController:_createGachaUI()
	gachaUI = New "ScreenGui" {
		Name = "GachaUI",
		Parent = playerGui,
		Enabled = Computed(function()
			return isGachaUIVisible:get()
		end),
		ResetOnSpawn = false,
		ZIndexBehavior = Enum.ZIndexBehavior.Sibling,
		
		[Children] = {
			-- 背景遮罩
			New "TextButton" {
				Name = "Background",
				Size = UDim2.fromScale(1, 1),
				BackgroundColor3 = Color3.fromRGB(0, 0, 0),
				BackgroundTransparency = 0.3,
				BorderSizePixel = 0,
				Text = "",

				[OnEvent "Activated"] = function()
					if not isAnimating:get() then
						self:HideGachaUI()
					end
				end,
			},
			
			-- 主要面板
			New "Frame" {
				Name = "MainPanel",
				Size = UDim2.fromScale(0.9, 0.9),
				Position = UDim2.fromScale(0.05, 0.05),
				BackgroundColor3 = Color3.fromRGB(40, 40, 40),
				BorderSizePixel = 0,
				
				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 15),
					},
					
					-- 標題
					New "TextLabel" {
						Name = "Title",
						Size = UDim2.new(1, 0, 0, 60),
						Position = UDim2.fromScale(0, 0),
						BackgroundTransparency = 1,
						Text = i18n.t("gacha.title"),
						TextColor3 = Color3.fromRGB(255, 215, 0),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
					},
					
					-- 關閉按鈕
					New "TextButton" {
						Name = "CloseButton",
						Size = UDim2.fromOffset(50, 50),
						Position = UDim2.new(1, -60, 0, 5),
						BackgroundColor3 = Color3.fromRGB(255, 100, 100),
						BorderSizePixel = 0,
						Text = "✕",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
						
						[OnEvent "Activated"] = function()
							if not isAnimating:get() then
								self:HideGachaUI()
							end
						end,
						
						[Children] = {
							New "UICorner" {
								CornerRadius = UDim.new(0, 10),
							}
						}
					},
					
					-- 抽卡按鈕區域
					self:_createGachaButtons(),
					
					-- 結果顯示區域
					self:_createResultsDisplay(),
					
					-- 歷史記錄
					self:_createHistoryPanel(),
				}
			}
		}
	}
end

-- 創建抽卡按鈕
function GachaController:_createGachaButtons()
	return New "Frame" {
		Name = "GachaButtons",
		Size = UDim2.new(1, -40, 0, 120),
		Position = UDim2.new(0, 20, 0, 80),
		BackgroundTransparency = 1,
		
		[Children] = {
			-- 單抽按鈕
			New "TextButton" {
				Name = "SinglePullButton",
				Size = UDim2.new(0, 200, 0, 80),
				Position = UDim2.new(0.25, -100, 0, 20),
				BackgroundColor3 = Color3.fromRGB(70, 130, 180),
				BorderSizePixel = 0,
				Text = "",
				
				[OnEvent "Activated"] = function()
					self:PerformSinglePull()
				end,
				
				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 12),
					},
					
					New "TextLabel" {
						Size = UDim2.new(1, 0, 0, 40),
						Position = UDim2.fromScale(0, 0),
						BackgroundTransparency = 1,
						Text = i18n.t("gacha.single_pull"),
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
					},
					
					New "TextLabel" {
						Size = UDim2.new(1, 0, 0, 30),
						Position = UDim2.new(0, 0, 0, 40),
						BackgroundTransparency = 1,
						Text = Computed(function()
							local costs = gachaCosts:get()
							return i18n.t("gacha.cost", {cost = costs.singlePullCost or 100})
						end),
						TextColor3 = Color3.fromRGB(255, 215, 0),
						TextScaled = true,
						Font = Enum.Font.Gotham,
					},
				}
			},
			
			-- 十連抽按鈕
			New "TextButton" {
				Name = "TenPullButton",
				Size = UDim2.new(0, 200, 0, 80),
				Position = UDim2.new(0.75, -100, 0, 20),
				BackgroundColor3 = Color3.fromRGB(255, 140, 0),
				BorderSizePixel = 0,
				Text = "",
				
				[OnEvent "Activated"] = function()
					self:PerformTenPull()
				end,
				
				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 12),
					},
					
					New "TextLabel" {
						Size = UDim2.new(1, 0, 0, 40),
						Position = UDim2.fromScale(0, 0),
						BackgroundTransparency = 1,
						Text = i18n.t("gacha.ten_pull"),
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.GothamBold,
					},
					
					New "TextLabel" {
						Size = UDim2.new(1, 0, 0, 30),
						Position = UDim2.new(0, 0, 0, 40),
						BackgroundTransparency = 1,
						Text = Computed(function()
							local costs = gachaCosts:get()
							return i18n.t("gacha.cost", {cost = costs.tenPullCost or 900})
						end),
						TextColor3 = Color3.fromRGB(255, 215, 0),
						TextScaled = true,
						Font = Enum.Font.Gotham,
					},
				}
			},
		}
	}
end

-- 創建結果顯示區域
function GachaController:_createResultsDisplay()
	return New "Frame" {
		Name = "ResultsDisplay",
		Size = UDim2.new(1, -40, 0, 300),
		Position = UDim2.new(0, 20, 0, 220),
		BackgroundColor3 = Color3.fromRGB(30, 30, 30),
		BorderSizePixel = 0,
		
		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 12),
			},
			
			New "ScrollingFrame" {
				Name = "ResultsScroll",
				Size = UDim2.new(1, -20, 1, -20),
				Position = UDim2.new(0, 10, 0, 10),
				BackgroundTransparency = 1,
				ScrollBarThickness = 8,
				
				[Children] = {
					New "UIGridLayout" {
						CellSize = UDim2.fromOffset(120, 150),
						CellPadding = UDim2.fromOffset(10, 10),
						SortOrder = Enum.SortOrder.LayoutOrder,
					},
					
					[Children] = Computed(function()
						local children = {}
						local results = currentResults:get()
						
						for i, result in ipairs(results) do
							children[i] = self:_createResultCard(result, i)
						end
						
						return children
					end)
				}
			}
		}
	}
end

-- 創建結果卡片
function GachaController:_createResultCard(result, index)
	local rarityData = result.type == "pet" and 
		PetDatabase.getRarity(result.rarity) or 
		WeaponDatabase.getRarity(result.rarity)
	
	return New "Frame" {
		Name = "ResultCard_" .. index,
		Size = UDim2.fromOffset(120, 150),
		BackgroundColor3 = rarityData.color,
		BorderSizePixel = 0,
		LayoutOrder = index,
		
		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},
			
			-- 物品圖標
			New "Frame" {
				Name = "Icon",
				Size = UDim2.fromOffset(80, 80),
				Position = UDim2.new(0.5, -40, 0, 10),
				BackgroundColor3 = Color3.fromRGB(60, 60, 60),
				BorderSizePixel = 0,
				
				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 8),
					},
					
					New "TextLabel" {
						Size = UDim2.fromScale(1, 1),
						BackgroundTransparency = 1,
						Text = result.type == "pet" and "🐾" or "⚔️",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.Gotham,
					},
				}
			},
			
			-- 物品名稱
			New "TextLabel" {
				Name = "ItemName",
				Size = UDim2.new(1, -10, 0, 30),
				Position = UDim2.new(0, 5, 0, 95),
				BackgroundTransparency = 1,
				Text = result.name,
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				TextWrapped = true,
			},
			
			-- 稀有度星級
			New "TextLabel" {
				Name = "Rarity",
				Size = UDim2.new(1, -10, 0, 20),
				Position = UDim2.new(0, 5, 0, 125),
				BackgroundTransparency = 1,
				Text = string.rep("⭐", rarityData.stars),
				TextColor3 = Color3.fromRGB(255, 215, 0),
				TextScaled = true,
				Font = Enum.Font.Gotham,
			},
			
			-- 新物品標記
			result.isNew and New "TextLabel" {
				Name = "NewLabel",
				Size = UDim2.fromOffset(40, 20),
				Position = UDim2.new(1, -45, 0, 5),
				BackgroundColor3 = Color3.fromRGB(255, 100, 100),
				BorderSizePixel = 0,
				Text = "NEW",
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
				
				[Children] = {
					New "UICorner" {
						CornerRadius = UDim.new(0, 4),
					}
				}
			} or nil,
		}
	}
end

-- 創建歷史記錄面板
function GachaController:_createHistoryPanel()
	return New "Frame" {
		Name = "HistoryPanel",
		Size = UDim2.new(1, -40, 0, 150),
		Position = UDim2.new(0, 20, 1, -170),
		BackgroundColor3 = Color3.fromRGB(30, 30, 30),
		BorderSizePixel = 0,
		
		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 12),
			},
			
			New "TextLabel" {
				Name = "HistoryTitle",
				Size = UDim2.new(1, 0, 0, 30),
				Position = UDim2.fromScale(0, 0),
				BackgroundTransparency = 1,
				Text = "抽卡歷史",
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
			},
			
			-- 保底計數顯示
			New "Frame" {
				Name = "PityDisplay",
				Size = UDim2.new(1, -20, 1, -40),
				Position = UDim2.new(0, 10, 0, 35),
				BackgroundTransparency = 1,
				
				[Children] = {
					New "UIListLayout" {
						FillDirection = Enum.FillDirection.Horizontal,
						SortOrder = Enum.SortOrder.LayoutOrder,
						Padding = UDim.new(0, 20),
					},
					
					[Children] = Computed(function()
						local children = {}
						local pity = gachaPity:get()
						
						children[1] = self:_createPityCounter("稀有保底", pity.rare or 0, 10)
						children[2] = self:_createPityCounter("傳說保底", pity.epic or 0, 50)
						children[3] = self:_createPityCounter("神話保底", pity.legendary or 0, 100)
						
						return children
					end)
				}
			}
		}
	}
end

-- 創建保底計數器
function GachaController:_createPityCounter(label, current, max)
	return New "Frame" {
		Size = UDim2.new(0, 150, 1, 0),
		BackgroundColor3 = Color3.fromRGB(50, 50, 50),
		BorderSizePixel = 0,
		
		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},
			
			New "TextLabel" {
				Size = UDim2.new(1, 0, 0, 30),
				Position = UDim2.fromScale(0, 0),
				BackgroundTransparency = 1,
				Text = label,
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.Gotham,
			},
			
			New "TextLabel" {
				Size = UDim2.new(1, 0, 0, 40),
				Position = UDim2.new(0, 0, 0, 30),
				BackgroundTransparency = 1,
				Text = current .. "/" .. max,
				TextColor3 = current >= max * 0.8 and Color3.fromRGB(255, 100, 100) or Color3.fromRGB(255, 215, 0),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
			},
			
			-- 進度條
			New "Frame" {
				Size = UDim2.new(0.8, 0, 0, 8),
				Position = UDim2.new(0.1, 0, 0, 75),
				BackgroundColor3 = Color3.fromRGB(30, 30, 30),
				BorderSizePixel = 0,
				
				[Children] = {
					New "Frame" {
						Size = UDim2.fromScale(math.min(current / max, 1), 1),
						BackgroundColor3 = current >= max * 0.8 and Color3.fromRGB(255, 100, 100) or Color3.fromRGB(255, 215, 0),
						BorderSizePixel = 0,
					}
				}
			}
		}
	}
end

-- 顯示抽卡 UI
function GachaController:ShowGachaUI()
	isGachaUIVisible:set(true)
	-- 請求最新的抽卡數據
	self.GachaService.GetGachaData:Fire()
end

-- 隱藏抽卡 UI
function GachaController:HideGachaUI()
	isGachaUIVisible:set(false)
	currentResults:set({})
end

-- 執行單抽
function GachaController:PerformSinglePull()
	if isAnimating:get() then return end
	
	isAnimating:set(true)
	currentResults:set({})
	
	-- 發送單抽請求
	self.GachaService.SinglePull:Fire("pet")
end

-- 執行十連抽
function GachaController:PerformTenPull()
	if isAnimating:get() then
		print("🎰 Ten pull blocked - animation in progress")
		return
	end

	print("🎰 Starting ten pull...")
	isAnimating:set(true)
	currentResults:set({})

	-- 發送十連抽請求
	self.GachaService.TenPull:Fire("pet")
	print("🎰 Ten pull request sent to server")
end

-- 處理抽卡結果
function GachaController:_handleGachaResult(results, poolType, isTenPull)
	print("🎰 Gacha result received:", #results, "items")

	-- 驗證結果
	if not results or #results == 0 then
		warn("❌ Invalid gacha results received")
		isAnimating:set(false)
		return
	end

	-- 顯示結果
	currentResults:set(results)

	-- 播放抽卡動畫（非阻塞）
	task.spawn(function()
		-- 簡化的動畫，避免長時間阻塞
		print("✨ Playing gacha animation for", #results, "results")

		-- 短暫延遲模擬動畫
		task.wait(isTenPull and 1 or 0.5)

		-- 重置動畫狀態
		isAnimating:set(false)

		-- 顯示通知
		if self.UIController then
			local message = isTenPull and "十連抽完成！" or "單抽完成！"
			self.UIController:ShowNotification(message, 3, Color3.fromRGB(255, 215, 0))
		end
	end)
end

-- 播放抽卡動畫（已移至 _handleGachaResult 中簡化處理）
function GachaController:_playGachaAnimation(results, isTenPull)
	-- 這個方法現在已經不使用，動畫邏輯已移至 _handleGachaResult
	-- 保留此方法以防將來需要更複雜的動畫效果
	print("✨ Gacha animation placeholder for", #results, "results")
end

return GachaController
