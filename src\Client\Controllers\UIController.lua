--[[
	UIController.lua - UI 控制器
	統一 UI 控制（切換頁面、提示）
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")
local TweenService = game:GetService("TweenService")
local Fusion = require(game:GetService("ReplicatedStorage").Packages.fusion)

-- Fusion 組件
local New = Fusion.New
local Children = Fusion.Children
local OnEvent = Fusion.OnEvent
local Value = Fusion.Value
local Computed = Fusion.Computed

-- 本地化
local i18n = require(game:GetService("ReplicatedStorage").Shared.i18n)

local UIController = Knit.CreateController({
	Name = "UIController",
})

-- 私有變量
local player = Players.LocalPlayer
local playerGui = player:WaitForChild("PlayerGui")

-- UI 狀態
local currentPage = Value("main")
local isUIVisible = Value(true)
local notifications = Value({})

-- UI 元素引用
local mainUI = nil
local notificationContainer = nil

function UIController:KnitStart()
	print("🎨 UIController started")
	
	-- 初始化 UI
	self:InitializeUI()
	
	-- 監聽其他控制器的 UI 事件
	self:_setupEventListeners()
end

function UIController:KnitInit()
	-- 初始化時不需要其他控制器
end

-- 初始化 UI
function UIController:InitializeUI()
	-- 創建主 UI 容器
	mainUI = New "ScreenGui" {
		Name = "MainUI",
		Parent = playerGui,
		ResetOnSpawn = false,
		ZIndexBehavior = Enum.ZIndexBehavior.Sibling,

		[Children] = {
			-- 主要 UI 框架
			New "Frame" {
				Name = "MainFrame",
				Size = UDim2.fromScale(1, 1),
				BackgroundTransparency = 1,

				[Children] = {
					-- 頂部狀態欄
					self:_createTopBar(),

					-- 底部按鈕欄
					self:_createBottomBar(),

					-- 頁面容器
					self:_createPageContainer(),
				}
			},
			
			-- 通知容器
			self:_createNotificationContainer(),
		}
	}
	
	print("🎨 Main UI initialized")
end

-- 創建頂部狀態欄
function UIController:_createTopBar()
	return New "Frame" {
		Name = "TopBar",
		Size = UDim2.new(1, 0, 0, 60),
		Position = UDim2.fromScale(0, 0),
		BackgroundColor3 = Color3.fromRGB(30, 30, 30),
		BorderSizePixel = 0,

		[Children] = {
			-- 玩家等級
			New "TextLabel" {
				Name = "LevelLabel",
				Size = UDim2.new(0, 100, 1, 0),
				Position = UDim2.fromScale(0, 0),
				BackgroundTransparency = 1,
				Text = Computed(function()
					return i18n.t("ui.labels.level") .. ": 1"
				end),
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.GothamBold,
			},

			-- 金幣顯示
			New "TextLabel" {
				Name = "CoinsLabel",
				Size = UDim2.new(0, 150, 1, 0),
				Position = UDim2.new(0, 120, 0, 0),
				BackgroundTransparency = 1,
				Text = Computed(function()
					return "💰 " .. i18n.t("ui.labels.coins") .. ": 100"
				end),
				TextColor3 = Color3.fromRGB(255, 215, 0),
				TextScaled = true,
				Font = Enum.Font.Gotham,
			},

			-- 血量條
			New "Frame" {
				Name = "HealthBar",
				Size = UDim2.new(0, 200, 0, 20),
				Position = UDim2.new(1, -220, 0.5, -10),
				BackgroundColor3 = Color3.fromRGB(50, 50, 50),
				BorderSizePixel = 0,

				[Children] = {
					New "Frame" {
						Name = "HealthFill",
						Size = UDim2.fromScale(1, 1), -- 這裡會動態更新
						BackgroundColor3 = Color3.fromRGB(255, 100, 100),
						BorderSizePixel = 0,
					},

					New "TextLabel" {
						Name = "HealthText",
						Size = UDim2.fromScale(1, 1),
						BackgroundTransparency = 1,
						Text = "100/100",
						TextColor3 = Color3.fromRGB(255, 255, 255),
						TextScaled = true,
						Font = Enum.Font.Gotham,
					},
				}
			},
		}
	}
end

-- 創建底部按鈕欄
function UIController:_createBottomBar()
	return New "Frame" {
		Name = "BottomBar",
		Size = UDim2.new(1, 0, 0, 80),
		Position = UDim2.new(0, 0, 1, -80),
		BackgroundColor3 = Color3.fromRGB(40, 40, 40),
		BorderSizePixel = 0,
		
		[Children] = {
			-- 攻擊按鈕
			self:_createButton("AttackButton", i18n.t("ui.buttons.attack"), UDim2.new(0, 100, 0, 60), UDim2.new(0, 10, 0.5, -30), function()
				self:TriggerAttack()
			end),
			
			-- 寵物按鈕
			self:_createButton("PetButton", i18n.t("ui.buttons.summon"), UDim2.new(0, 100, 0, 60), UDim2.new(0, 120, 0.5, -30), function()
				self:ShowPage("pet")
			end),
			
			-- 抽卡按鈕
			self:_createButton("GachaButton", i18n.t("ui.buttons.gacha"), UDim2.new(0, 100, 0, 60), UDim2.new(0, 230, 0.5, -30), function()
				self:ShowPage("gacha")
			end),
			
			-- 設定按鈕
			self:_createButton("SettingsButton", i18n.t("ui.buttons.settings"), UDim2.new(0, 100, 0, 60), UDim2.new(1, -110, 0.5, -30), function()
				self:ShowPage("settings")
			end),
		}
	}
end

-- 創建頁面容器
function UIController:_createPageContainer()
	return New "Frame" {
		Name = "PageContainer",
		Size = UDim2.new(1, 0, 1, -140), -- 扣除頂部和底部欄的高度
		Position = UDim2.new(0, 0, 0, 60),
		BackgroundTransparency = 1,
		
		[Children] = {
			-- 這裡會動態載入不同的頁面
		}
	}
end

-- 創建通知容器
function UIController:_createNotificationContainer()
	notificationContainer = New "Frame" {
		Name = "NotificationContainer",
		Size = UDim2.new(0, 300, 1, 0),
		Position = UDim2.new(1, -320, 0, 20),
		BackgroundTransparency = 1,

		[Children] = Computed(function()
			local children = {}
			local notifs = notifications:get()
			
			for i, notification in ipairs(notifs) do
				children[i] = self:_createNotification(notification, i)
			end
			
			return children
		end)
	}
	
	return notificationContainer
end

-- 創建按鈕
function UIController:_createButton(name, text, size, position, callback)
	return New "TextButton" {
		Name = name,
		Size = size,
		Position = position,
		BackgroundColor3 = Color3.fromRGB(70, 130, 180),
		BorderSizePixel = 0,
		Text = text,
		TextColor3 = Color3.fromRGB(255, 255, 255),
		TextScaled = true,
		Font = Enum.Font.GothamBold,

		[OnEvent "Activated"] = callback,

		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			}
		}
	}
end

-- 創建通知
function UIController:_createNotification(notification, index)
	return New "Frame" {
		Name = "Notification_" .. index,
		Size = UDim2.new(1, 0, 0, 60),
		Position = UDim2.new(0, 0, 0, (index - 1) * 70),
		BackgroundColor3 = notification.color or Color3.fromRGB(50, 50, 50),
		BorderSizePixel = 0,

		[Children] = {
			New "UICorner" {
				CornerRadius = UDim.new(0, 8),
			},

			New "TextLabel" {
				Size = UDim2.new(1, -20, 1, 0),
				Position = UDim2.new(0, 10, 0, 0),
				BackgroundTransparency = 1,
				Text = notification.text,
				TextColor3 = Color3.fromRGB(255, 255, 255),
				TextScaled = true,
				Font = Enum.Font.Gotham,
				TextWrapped = true,
			}
		}
	}
end

-- 設置事件監聽
function UIController:_setupEventListeners()
	-- 監聽鍵盤輸入
	game:GetService("UserInputService").InputBegan:Connect(function(input, gameProcessed)
		if gameProcessed then return end
		
		if input.KeyCode == Enum.KeyCode.Tab then
			-- 切換 UI 顯示/隱藏
			self:ToggleUI()
		elseif input.KeyCode == Enum.KeyCode.Space then
			-- 空格鍵攻擊
			self:TriggerAttack()
		end
	end)
end

-- 顯示頁面
function UIController:ShowPage(pageName)
	currentPage:set(pageName)
	print("🎨 Switching to page:", pageName)

	-- 根據頁面名稱調用對應的控制器
	if pageName == "pet" then
		-- 顯示寵物UI - 通過PetController
		local PetController = Knit.GetController("PetController")
		if PetController and PetController.ShowPetUI then
			PetController:ShowPetUI()
		else
			self:ShowNotification("寵物系統暫時不可用", 3, Color3.fromRGB(255, 200, 100))
		end
	elseif pageName == "gacha" then
		-- 顯示抽卡UI
		local GachaController = Knit.GetController("GachaController")
		if GachaController and GachaController.ShowGachaUI then
			GachaController:ShowGachaUI()
		else
			self:ShowNotification("抽卡系統暫時不可用", 3, Color3.fromRGB(255, 200, 100))
		end
	elseif pageName == "settings" then
		-- 顯示設定UI
		self:ShowNotification("設定功能開發中...", 3, Color3.fromRGB(100, 200, 255))
	else
		self:ShowNotification("未知頁面: " .. pageName, 3, Color3.fromRGB(255, 100, 100))
	end
end

-- 切換 UI 顯示
function UIController:ToggleUI()
	local visible = not isUIVisible:get()
	isUIVisible:set(visible)
	
	if mainUI then
		mainUI.Enabled = visible
	end
end

-- 顯示通知
function UIController:ShowNotification(text, duration, color)
	local notification = {
		text = text,
		color = color or Color3.fromRGB(70, 130, 180),
		timestamp = tick(),
	}
	
	local currentNotifs = notifications:get()
	local newNotifs = {}
	
	-- 複製現有通知
	for i, notif in ipairs(currentNotifs) do
		table.insert(newNotifs, notif)
	end
	
	-- 添加新通知
	table.insert(newNotifs, notification)
	
	-- 限制通知數量
	if #newNotifs > 5 then
		table.remove(newNotifs, 1)
	end
	
	notifications:set(newNotifs)
	
	-- 自動移除通知
	task.wait(duration or 3)
	self:RemoveNotification(notification)
end

-- 移除通知
function UIController:RemoveNotification(targetNotification)
	local currentNotifs = notifications:get()
	local newNotifs = {}
	
	for i, notif in ipairs(currentNotifs) do
		if notif ~= targetNotification then
			table.insert(newNotifs, notif)
		end
	end
	
	notifications:set(newNotifs)
end

-- 觸發攻擊
function UIController:TriggerAttack()
	local CombatController = Knit.GetController("CombatController")
	if CombatController then
		CombatController:RequestAttack()
	end
end

-- 更新玩家數據顯示
function UIController:UpdatePlayerData(playerData)
	-- 更新 UI 顯示
	-- 這裡可以更新等級、金幣、血量等顯示
end

-- 更新血量顯示
function UIController:UpdateHealth(current, maximum)
	if mainUI then
		local healthBar = mainUI:FindFirstChild("MainFrame"):FindFirstChild("TopBar"):FindFirstChild("HealthBar")
		if healthBar then
			local healthFill = healthBar:FindFirstChild("HealthFill")
			local healthText = healthBar:FindFirstChild("HealthText")
			
			if healthFill and healthText then
				local healthPercent = current / maximum
				healthFill.Size = UDim2.fromScale(healthPercent, 1)
				healthText.Text = current .. "/" .. maximum
			end
		end
	end
end

return UIController
