--[[
	CombatService.lua - 戰鬥服務（重構版）
	整合 Matter ECS 的戰鬥系統
]]

local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Players = game:GetService("Players")

-- ECS 組件
local PositionComponent = require(game:GetService("ReplicatedStorage").ECS.Components.PositionComponent)
local HealthComponent = require(game:GetService("ReplicatedStorage").ECS.Components.HealthComponent)
local DamageComponent = require(game:GetService("ReplicatedStorage").ECS.Components.DamageComponent)
local SwordSwingComponent = require(game:GetService("ReplicatedStorage").ECS.Components.SwordSwingComponent)
local TargetComponent = require(game:GetService("ReplicatedStorage").ECS.Components.TargetComponent)

local CombatService = Knit.CreateService({
	Name = "CombatService",
	Client = {
		-- 客戶端可調用的方法
		AttackTarget = Knit.CreateSignal(),
		PetAttackTarget = Knit.CreateSignal(),
		
		-- 客戶端事件
		AttackResult = Knit.CreateSignal(),
		TakeDamage = Knit.CreateSignal(),
		CombatUpdate = Knit.CreateSignal(),
		TargetDefeated = Knit.CreateSignal(),
	},
})

function CombatService:KnitInit()
	-- 獲取其他服務
	self.PlayerService = Knit.GetService("PlayerService")
	self.PetService = Knit.GetService("PetService")
end

function CombatService:KnitStart()
	print("⚔️ CombatService started (ECS Version)")

	-- 獲取 Matter World
	self.world = _G.MatterWorld
	if not self.world then
		warn("❌ Matter World not found!")
		return
	end

	-- 連接客戶端信號 (在 KnitStart 中確保信號已轉換)
	self.Client.AttackTarget:Connect(function(player, targetType, targetId)
		self:_handlePlayerAttack(player, targetType, targetId)
	end)

	self.Client.PetAttackTarget:Connect(function(player, petId, targetType, targetId)
		self:_handlePetAttack(player, petId, targetType, targetId)
	end)
end

-- 處理玩家攻擊
function CombatService:_handlePlayerAttack(player, targetType, targetId)
	local playerEntityId = self.PlayerService:GetPlayerEntityId(player)
	if not playerEntityId then
		warn("❌ Player entity not found:", player.Name)
		return
	end
	
	-- 檢查攻擊冷卻
	local targetComponent = self.world:get(playerEntityId, TargetComponent)
	local currentTime = tick()
	
	if targetComponent and (currentTime - targetComponent.lastAttackTime) < targetComponent.attackCooldown then
		-- 攻擊冷卻中
		return
	end
	
	-- 檢查攻擊範圍
	if not self:_isInAttackRange(playerEntityId, targetId) then
		-- 距離太遠
		return
	end
	
	-- 執行攻擊
	self:_executeAttack(playerEntityId, targetId, player)
end

-- 處理寵物攻擊
function CombatService:_handlePetAttack(player, petId, targetType, targetId)
	local petEntityId = self.PetService:GetActivePetEntityId(player)
	if not petEntityId then
		warn("❌ Pet entity not found for player:", player.Name)
		return
	end
	
	-- 檢查寵物是否在攻擊範圍內
	if not self:_isInAttackRange(petEntityId, targetId) then
		return
	end
	
	-- 執行寵物攻擊
	self:_executeAttack(petEntityId, targetId, player)
end

-- 執行攻擊
function CombatService:_executeAttack(attackerEntityId, targetEntityId, player)
	local attackerDamage = self.world:get(attackerEntityId, DamageComponent)
	local attackerPosition = self.world:get(attackerEntityId, PositionComponent)
	
	if not attackerDamage or not attackerPosition then
		warn("❌ Attacker missing components")
		return
	end
	
	-- 創建或更新劍擊組件
	local swordSwing = self.world:get(attackerEntityId, SwordSwingComponent)
	if not swordSwing then
		-- 添加劍擊組件
		self.world:insert(attackerEntityId, SwordSwingComponent({
			isSwinging = true,
			swingDuration = 0.5,
			swingStartTime = tick(),
			range = 10,
			damage = attackerDamage.attack,
			weaponId = "",
			hasHit = {},
		}))
	else
		-- 更新劍擊組件
		self.world:insert(attackerEntityId, swordSwing:patch({
			isSwinging = true,
			swingStartTime = tick(),
			damage = attackerDamage.attack,
			hasHit = {},
		}))
	end
	
	-- 更新目標組件
	local targetComponent = self.world:get(attackerEntityId, TargetComponent)
	if not targetComponent then
		self.world:insert(attackerEntityId, TargetComponent({
			targetId = targetEntityId,
			targetType = "Monster",
			lastTargetTime = tick(),
			attackRange = 10,
			canAttack = true,
			lastAttackTime = tick(),
			attackCooldown = 1.5,
		}))
	else
		self.world:insert(attackerEntityId, targetComponent:patch({
			targetId = targetEntityId,
			lastAttackTime = tick(),
		}))
	end
	
	-- 通知客戶端攻擊開始
	self.Client.AttackResult:Fire(player, targetEntityId, attackerDamage.attack, false)
	
	print("⚔️ Attack initiated by", player and player.Name or "Entity", attackerEntityId)
end

-- 檢查攻擊範圍
function CombatService:_isInAttackRange(attackerEntityId, targetEntityId)
	local attackerPos = self.world:get(attackerEntityId, PositionComponent)
	local targetPos = self.world:get(targetEntityId, PositionComponent)
	
	if not attackerPos or not targetPos then
		return false
	end
	
	local distance = (attackerPos.position - targetPos.position).Magnitude
	return distance <= 10 -- 攻擊範圍10格
end

-- 處理傷害（由 ECS 系統調用）
function CombatService:HandleDamage(targetEntityId, damage, attackerEntityId, sourcePlayer)
	local targetHealth = self.world:get(targetEntityId, HealthComponent)
	if not targetHealth then return end
	
	local newHealth = math.max(0, targetHealth.current - damage)
	local wasKilled = newHealth <= 0
	
	-- 更新血量
	self.world:insert(targetEntityId, targetHealth:patch({
		current = newHealth,
		lastDamageTime = tick(),
		isDead = wasKilled,
	}))
	
	-- 通知客戶端傷害結果
	if sourcePlayer then
		self.Client.TakeDamage:Fire(sourcePlayer, damage, targetEntityId)
		self.Client.CombatUpdate:Fire(sourcePlayer, newHealth, targetHealth.maximum)
		
		if wasKilled then
			self.Client.TargetDefeated:Fire(sourcePlayer, targetEntityId)
			self:_handleTargetDefeated(targetEntityId, sourcePlayer)
		end
	end
	
	print("⚔️ Damage dealt:", damage, "New health:", newHealth)
end

-- 處理目標被擊敗
function CombatService:_handleTargetDefeated(targetEntityId, player)
	-- 給予經驗值和金幣獎勵
	local profile = self.PlayerService:GetPlayerProfile(player)
	if profile then
		-- 基礎獎勵
		local expReward = math.random(10, 25)
		local coinReward = math.random(5, 15)
		
		-- 添加經驗值
		local PlayerProfile = require(game:GetService("ReplicatedStorage").Shared.Profile.PlayerProfile)
		PlayerProfile.addExperience(player, expReward)
		PlayerProfile.addCoins(player, coinReward)
		
		-- 更新統計
		profile.Data.statistics.monstersKilled = profile.Data.statistics.monstersKilled + 1
		profile.Data.statistics.damageDealt = profile.Data.statistics.damageDealt + 1
		
		print("🎉 Player", player.Name, "defeated target! Rewards: EXP", expReward, "Coins", coinReward)
	end
end

-- 獲取實體血量
function CombatService:GetEntityHealth(entityId)
	return self.world:get(entityId, HealthComponent)
end

-- 治療實體
function CombatService:HealEntity(entityId, healAmount)
	local health = self.world:get(entityId, HealthComponent)
	if health and not health.isDead then
		local newHealth = math.min(health.maximum, health.current + healAmount)
		self.world:insert(entityId, health:patch({
			current = newHealth,
		}))
		return newHealth
	end
	return 0
end

-- 復活實體
function CombatService:ReviveEntity(entityId, healthPercent)
	local health = self.world:get(entityId, HealthComponent)
	if health and health.isDead then
		local newHealth = math.floor(health.maximum * (healthPercent or 0.5))
		self.world:insert(entityId, health:patch({
			current = newHealth,
			isDead = false,
		}))
		return true
	end
	return false
end



return CombatService
