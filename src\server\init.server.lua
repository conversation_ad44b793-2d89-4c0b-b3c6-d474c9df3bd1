--[[
	Pet RPG - Server Initialization
	使用 Knit 框架初始化服務端
]]

-- 載入依賴
local Knit = require(game:GetService("ReplicatedStorage").Packages.knit)
local Matter = require(game:GetService("ReplicatedStorage").Packages.matter)
local RunService = game:GetService("RunService")

-- 創建 Matter World
local world = Matter.World.new()
_G.MatterWorld = world

-- 載入所有服務
local Services = script.Services
for _, serviceModule in pairs(Services:GetChildren()) do
	if serviceModule:IsA("ModuleScript") then
		require(serviceModule)
	end
end

-- 載入 ECS 系統
local function loadECSSystems()
	local systems = {}
	local SystemsFolder = game:GetService("ReplicatedStorage").ECS.Systems

	if SystemsFolder then
		for _, systemModule in pairs(SystemsFolder:GetChildren()) do
			if systemModule:IsA("ModuleScript") then
				local systemFunction = require(systemModule)
				if typeof(systemFunction) == "function" then
					table.insert(systems, systemFunction)
					print("📦 Loaded ECS System:", systemModule.Name)
				end
			end
		end
	end

	return systems
end

-- 啟動 Knit 服務端
Knit.Start():andThen(function()
	print("🎮 Pet RPG Server Started!")
	print("⚙️ Matter World initialized")

	-- 確保服務依賴關係
	local DataService = Knit.GetService("DataService")
	local PetService = Knit.GetService("PetService")
	local MonsterService = Knit.GetService("MonsterService")
	local CombatService = Knit.GetService("CombatService")

	-- 設置服務間依賴
	PetService.DataService = DataService
	MonsterService.DataService = DataService
	CombatService.DataService = DataService

	-- 載入並啟動 ECS 系統
	local systems = loadECSSystems()

	-- 開始 ECS 主循環
	RunService.Heartbeat:Connect(function(deltaTime)
		for _, system in pairs(systems) do
			system(world, deltaTime)
		end
	end)

	print("🔄 ECS Systems running:", #systems)
	print("⚔️ Combat system initialized!")
end):catch(function(err)
	warn("❌ Server startup failed:", err)
end)
