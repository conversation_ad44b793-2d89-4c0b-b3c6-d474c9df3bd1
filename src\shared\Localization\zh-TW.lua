--[[
	zh-TW.lua - 繁體中文語言包
]]

return {
	-- 通用
	common = {
		ok = "確定",
		cancel = "取消",
		close = "關閉",
		back = "返回",
		next = "下一步",
		previous = "上一步",
		confirm = "確認",
		loading = "載入中...",
		error = "錯誤",
		success = "成功",
		warning = "警告",
		info = "資訊",
	},
	
	-- UI 介面
	ui = {
		buttons = {
			attack = "攻擊",
			summon = "召喚",
			recall = "收回",
			gacha = "抽卡",
			inventory = "背包",
			settings = "設定",
			quit = "離開",
		},
		
		menus = {
			main = "主選單",
			pet = "寵物",
			weapon = "武器",
			gacha = "抽卡",
			profile = "個人資料",
			achievements = "成就",
			settings = "設定",
		},
		
		labels = {
			level = "等級",
			experience = "經驗值",
			health = "血量",
			attack = "攻擊力",
			defense = "防禦力",
			speed = "速度",
			coins = "金幣",
			gems = "寶石",
			rarity = "稀有度",
		},
	},
	
	-- 寵物系統
	pets = {
		title = "寵物系統",
		summon_success = "成功召喚 {petName}！",
		summon_failed = "召喚失敗",
		recall_success = "已收回寵物",
		not_owned = "你還沒有這隻寵物",
		already_summoned = "已有寵物在場",
		level_up = "{petName} 升級了！",
		
		rarities = {
			Common = "普通",
			Uncommon = "稀有",
			Rare = "史詩",
			Epic = "傳說",
			Legendary = "神話",
		},
		
		abilities = {
			Bounce = "彈跳攻擊",
			Howl = "嚎叫增益",
			Fireball = "火球攻擊",
			Burn = "燃燒效果",
			IceBreath = "冰息攻擊",
			Freeze = "冰凍效果",
			DragonBreath = "龍息攻擊",
			Flight = "飛行能力",
			Roar = "龍吼威嚇",
		},
	},
	
	-- 戰鬥系統
	combat = {
		attack_hit = "命中！造成 {damage} 點傷害",
		critical_hit = "暴擊！造成 {damage} 點傷害",
		enemy_defeated = "敵人被擊敗！",
		player_died = "你死了！",
		pet_died = "寵物死亡",
		out_of_range = "距離太遠",
		cooldown = "技能冷卻中",
	},
	
	-- 抽卡系統
	gacha = {
		title = "抽卡系統",
		single_pull = "單抽",
		ten_pull = "十連抽",
		cost = "花費：{cost} 金幣",
		insufficient_coins = "金幣不足",
		congratulations = "恭喜獲得",
		new_pet = "新寵物！",
		duplicate = "重複寵物（已轉換為經驗值）",
		
		results = {
			common = "獲得普通寵物",
			uncommon = "獲得稀有寵物",
			rare = "獲得史詩寵物！",
			epic = "獲得傳說寵物！！",
			legendary = "獲得神話寵物！！！",
		},
	},
	
	-- 設定
	settings = {
		title = "遊戲設定",
		language = "語言",
		music_volume = "音樂音量",
		sfx_volume = "音效音量",
		auto_attack = "自動攻擊",
		show_damage = "顯示傷害數字",
		graphics_quality = "畫質設定",
		
		quality_levels = {
			low = "低",
			medium = "中",
			high = "高",
			ultra = "極高",
		},
	},
	
	-- 成就
	achievements = {
		title = "成就系統",
		unlocked = "成就解鎖！",
		progress = "進度：{current}/{total}",
		
		names = {
			first_pet = "第一隻寵物",
			pet_collector = "寵物收集家",
			monster_slayer = "怪物獵人",
			level_up = "升級達人",
			gacha_master = "抽卡大師",
		},
		
		descriptions = {
			first_pet = "獲得你的第一隻寵物",
			pet_collector = "收集 10 隻不同的寵物",
			monster_slayer = "擊敗 100 隻怪物",
			level_up = "達到 10 級",
			gacha_master = "進行 100 次抽卡",
		},
	},
	
	-- 錯誤訊息
	errors = {
		connection_lost = "連線中斷",
		data_load_failed = "數據載入失敗",
		save_failed = "保存失敗",
		invalid_action = "無效操作",
		server_error = "伺服器錯誤",
		timeout = "操作超時",
	},
	
	-- 通知訊息
	notifications = {
		welcome = "歡迎來到遊戲！",
		daily_reward = "獲得每日獎勵！",
		level_up = "恭喜升級到 {level} 級！",
		new_achievement = "獲得新成就：{achievement}",
		maintenance = "伺服器維護中",
	},
}
