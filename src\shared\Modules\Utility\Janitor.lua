--[[
	Janitor.lua - 資源清理管理器
	用於管理和清理各種資源（連接、實例、線程等）
]]

local Janitor = {}
Janitor.__index = Janitor

-- 支持的清理方法
local CLEANUP_METHODS = {
	["function"] = function(item)
		item()
	end,
	["RBXScriptConnection"] = function(item)
		item:Disconnect()
	end,
	["Instance"] = function(item)
		item:Destroy()
	end,
	["thread"] = function(item)
		task.cancel(item)
	end,
}

function Janitor.new()
	local self = setmetatable({
		_items = {},
		_cleanupMethods = {},
	}, Janitor)
	
	return self
end

-- 添加需要清理的項目
function Janitor:Add(item, methodName, index)
	if item == nil then
		return
	end
	
	-- 確定清理方法
	local cleanupMethod
	if methodName then
		if type(methodName) == "string" then
			-- 使用指定的方法名
			cleanupMethod = function(obj)
				if obj[methodName] then
					obj[methodName](obj)
				end
			end
		elseif type(methodName) == "function" then
			-- 使用自定義函數
			cleanupMethod = methodName
		end
	else
		-- 自動檢測清理方法
		local itemType = typeof(item)
		cleanupMethod = CLEANUP_METHODS[itemType]
		
		if not cleanupMethod and type(item) == "table" then
			-- 檢查是否有常見的清理方法
			if item.Destroy then
				cleanupMethod = function(obj) obj:Destroy() end
			elseif item.Disconnect then
				cleanupMethod = function(obj) obj:Disconnect() end
			elseif item.disconnect then
				cleanupMethod = function(obj) obj:disconnect() end
			end
		end
	end
	
	if not cleanupMethod then
		warn("Janitor: No cleanup method found for", typeof(item))
		return
	end
	
	-- 使用索引或自動生成
	local key = index or #self._items + 1
	
	-- 如果該索引已存在，先清理舊項目
	if self._items[key] then
		self:Remove(key)
	end
	
	self._items[key] = item
	self._cleanupMethods[key] = cleanupMethod
	
	return key
end

-- 移除並清理指定項目
function Janitor:Remove(index)
	local item = self._items[index]
	local cleanupMethod = self._cleanupMethods[index]
	
	if item and cleanupMethod then
		local success, errorMessage = pcall(cleanupMethod, item)
		if not success then
			warn("Janitor cleanup error:", errorMessage)
		end
		
		self._items[index] = nil
		self._cleanupMethods[index] = nil
	end
end

-- 獲取項目
function Janitor:Get(index)
	return self._items[index]
end

-- 清理所有項目
function Janitor:Cleanup()
	for index in pairs(self._items) do
		self:Remove(index)
	end
end

-- 清理並銷毀 Janitor
function Janitor:Destroy()
	self:Cleanup()
	setmetatable(self, nil)
end

-- 添加連接
function Janitor:AddConnection(connection, index)
	return self:Add(connection, "Disconnect", index)
end

-- 添加實例
function Janitor:AddInstance(instance, index)
	return self:Add(instance, "Destroy", index)
end

-- 添加線程
function Janitor:AddThread(thread, index)
	return self:Add(thread, task.cancel, index)
end

-- 添加清理函數
function Janitor:AddCleanupFunction(func, index)
	return self:Add(func, nil, index)
end

-- 添加 Promise
function Janitor:AddPromise(promise, index)
	if promise and promise.cancel then
		return self:Add(promise, "cancel", index)
	else
		warn("Janitor: Promise does not have a cancel method")
	end
end

-- 添加 Tween
function Janitor:AddTween(tween, index)
	return self:Add(tween, "Cancel", index)
end

-- 添加 Sound
function Janitor:AddSound(sound, index)
	return self:Add(sound, function(s)
		s:Stop()
		s:Destroy()
	end, index)
end

-- 批量添加
function Janitor:AddArray(items, methodName)
	local indices = {}
	for i, item in ipairs(items) do
		local index = self:Add(item, methodName)
		table.insert(indices, index)
	end
	return indices
end

-- 檢查是否為空
function Janitor:IsEmpty()
	return next(self._items) == nil
end

-- 獲取項目數量
function Janitor:GetCount()
	local count = 0
	for _ in pairs(self._items) do
		count = count + 1
	end
	return count
end

-- 獲取所有索引
function Janitor:GetIndices()
	local indices = {}
	for index in pairs(self._items) do
		table.insert(indices, index)
	end
	return indices
end

-- 清理指定類型的項目
function Janitor:CleanupType(itemType)
	for index, item in pairs(self._items) do
		if typeof(item) == itemType then
			self:Remove(index)
		end
	end
end

-- 清理匹配條件的項目
function Janitor:CleanupWhere(predicate)
	if type(predicate) ~= "function" then
		warn("Janitor:CleanupWhere expects a function")
		return
	end
	
	for index, item in pairs(self._items) do
		if predicate(item, index) then
			self:Remove(index)
		end
	end
end

-- 鏈式調用支持
function Janitor:LinkToInstance(instance)
	if not instance or not instance.Parent then
		warn("Janitor:LinkToInstance expects a valid Instance")
		return self
	end
	
	local connection = instance.AncestryChanged:Connect(function()
		if not instance.Parent then
			self:Destroy()
		end
	end)
	
	self:AddConnection(connection)
	return self
end

-- 鏈式調用支持
function Janitor:LinkToHeartbeat(callback)
	if type(callback) ~= "function" then
		warn("Janitor:LinkToHeartbeat expects a function")
		return self
	end
	
	local connection = game:GetService("RunService").Heartbeat:Connect(callback)
	self:AddConnection(connection)
	return self
end

return Janitor
