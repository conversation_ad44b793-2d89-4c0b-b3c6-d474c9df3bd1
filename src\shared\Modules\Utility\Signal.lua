--[[
	Signal.lua - 信號系統
	提供類似 RBXScriptSignal 的自定義信號實現
]]

local Signal = {}
Signal.__index = Signal

-- 連接對象
local Connection = {}
Connection.__index = Connection

function Connection.new(signal, callback)
	local self = setmetatable({
		_signal = signal,
		_callback = callback,
		_connected = true,
	}, Connection)
	
	return self
end

function Connection:Disconnect()
	if not self._connected then
		return
	end
	
	self._connected = false
	
	-- 從信號的連接列表中移除
	local connections = self._signal._connections
	for i = #connections, 1, -1 do
		if connections[i] == self then
			table.remove(connections, i)
			break
		end
	end
	
	self._signal = nil
	self._callback = nil
end

function Connection:IsConnected()
	return self._connected
end

-- 信號類
function Signal.new()
	local self = setmetatable({
		_connections = {},
		_bindableEvent = nil,
	}, Signal)
	
	return self
end

-- 連接回調函數
function Signal:Connect(callback)
	if type(callback) ~= "function" then
		error("Callback must be a function", 2)
	end
	
	local connection = Connection.new(self, callback)
	table.insert(self._connections, connection)
	
	return connection
end

-- 連接一次性回調函數
function Signal:Once(callback)
	if type(callback) ~= "function" then
		error("Callback must be a function", 2)
	end
	
	local connection
	connection = self:Connect(function(...)
		connection:Disconnect()
		callback(...)
	end)
	
	return connection
end

-- 觸發信號
function Signal:Fire(...)
	local connections = self._connections
	
	-- 複製連接列表以防在回調中修改
	local connectionsToCall = {}
	for i = 1, #connections do
		if connections[i]._connected then
			connectionsToCall[i] = connections[i]
		end
	end
	
	-- 調用所有連接的回調
	for i = 1, #connectionsToCall do
		local connection = connectionsToCall[i]
		if connection and connection._connected then
			local success, errorMessage = pcall(connection._callback, ...)
			if not success then
				warn("Error in signal callback:", errorMessage)
			end
		end
	end
end

-- 等待信號觸發
function Signal:Wait()
	if not self._bindableEvent then
		self._bindableEvent = Instance.new("BindableEvent")
	end
	
	local connection
	connection = self:Connect(function(...)
		connection:Disconnect()
		self._bindableEvent:Fire(...)
	end)
	
	return self._bindableEvent.Event:Wait()
end

-- 斷開所有連接
function Signal:DisconnectAll()
	local connections = self._connections
	for i = #connections, 1, -1 do
		connections[i]:Disconnect()
	end
end

-- 銷毀信號
function Signal:Destroy()
	self:DisconnectAll()
	
	if self._bindableEvent then
		self._bindableEvent:Destroy()
		self._bindableEvent = nil
	end
	
	setmetatable(self, nil)
end

-- 獲取連接數量
function Signal:GetConnectionCount()
	local count = 0
	for _, connection in ipairs(self._connections) do
		if connection._connected then
			count = count + 1
		end
	end
	return count
end

-- 檢查是否有連接
function Signal:HasConnections()
	return self:GetConnectionCount() > 0
end

return Signal
