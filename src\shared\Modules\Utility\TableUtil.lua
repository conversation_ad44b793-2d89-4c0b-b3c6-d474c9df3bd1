--[[
	TableUtil.lua - 表格工具函數
	提供常用的表格操作方法
]]

local TableUtil = {}

-- 深拷貝表格
function TableUtil.deepCopy(original)
	if type(original) ~= "table" then
		return original
	end
	
	local copy = {}
	for key, value in pairs(original) do
		copy[TableUtil.deepCopy(key)] = TableUtil.deepCopy(value)
	end
	
	return setmetatable(copy, getmetatable(original))
end

-- 淺拷貝表格
function TableUtil.shallowCopy(original)
	if type(original) ~= "table" then
		return original
	end
	
	local copy = {}
	for key, value in pairs(original) do
		copy[key] = value
	end
	
	return setmetatable(copy, getmetatable(original))
end

-- 合併表格
function TableUtil.merge(...)
	local result = {}
	local tables = {...}
	
	for _, tbl in ipairs(tables) do
		if type(tbl) == "table" then
			for key, value in pairs(tbl) do
				result[key] = value
			end
		end
	end
	
	return result
end

-- 深度合併表格
function TableUtil.deepMerge(...)
	local result = {}
	local tables = {...}
	
	for _, tbl in ipairs(tables) do
		if type(tbl) == "table" then
			for key, value in pairs(tbl) do
				if type(value) == "table" and type(result[key]) == "table" then
					result[key] = TableUtil.deepMerge(result[key], value)
				else
					result[key] = value
				end
			end
		end
	end
	
	return result
end

-- 檢查表格是否為空
function TableUtil.isEmpty(tbl)
	if type(tbl) ~= "table" then
		return true
	end
	
	return next(tbl) == nil
end

-- 獲取表格長度（包括非數字索引）
function TableUtil.getLength(tbl)
	if type(tbl) ~= "table" then
		return 0
	end
	
	local count = 0
	for _ in pairs(tbl) do
		count = count + 1
	end
	
	return count
end

-- 檢查表格是否包含值
function TableUtil.contains(tbl, value)
	if type(tbl) ~= "table" then
		return false
	end
	
	for _, v in pairs(tbl) do
		if v == value then
			return true
		end
	end
	
	return false
end

-- 查找值的索引
function TableUtil.indexOf(tbl, value)
	if type(tbl) ~= "table" then
		return nil
	end
	
	for i, v in ipairs(tbl) do
		if v == value then
			return i
		end
	end
	
	return nil
end

-- 移除值
function TableUtil.removeValue(tbl, value)
	if type(tbl) ~= "table" then
		return false
	end
	
	for i = #tbl, 1, -1 do
		if tbl[i] == value then
			table.remove(tbl, i)
			return true
		end
	end
	
	return false
end

-- 過濾表格
function TableUtil.filter(tbl, predicate)
	if type(tbl) ~= "table" or type(predicate) ~= "function" then
		return {}
	end
	
	local result = {}
	for key, value in pairs(tbl) do
		if predicate(value, key) then
			result[key] = value
		end
	end
	
	return result
end

-- 映射表格
function TableUtil.map(tbl, mapper)
	if type(tbl) ~= "table" or type(mapper) ~= "function" then
		return {}
	end
	
	local result = {}
	for key, value in pairs(tbl) do
		result[key] = mapper(value, key)
	end
	
	return result
end

-- 歸約表格
function TableUtil.reduce(tbl, reducer, initialValue)
	if type(tbl) ~= "table" or type(reducer) ~= "function" then
		return initialValue
	end
	
	local accumulator = initialValue
	for key, value in pairs(tbl) do
		accumulator = reducer(accumulator, value, key)
	end
	
	return accumulator
end

-- 獲取所有鍵
function TableUtil.keys(tbl)
	if type(tbl) ~= "table" then
		return {}
	end
	
	local keys = {}
	for key in pairs(tbl) do
		table.insert(keys, key)
	end
	
	return keys
end

-- 獲取所有值
function TableUtil.values(tbl)
	if type(tbl) ~= "table" then
		return {}
	end
	
	local values = {}
	for _, value in pairs(tbl) do
		table.insert(values, value)
	end
	
	return values
end

-- 反轉數組
function TableUtil.reverse(tbl)
	if type(tbl) ~= "table" then
		return {}
	end
	
	local result = {}
	for i = #tbl, 1, -1 do
		table.insert(result, tbl[i])
	end
	
	return result
end

-- 隨機選擇元素
function TableUtil.randomChoice(tbl)
	if type(tbl) ~= "table" or TableUtil.isEmpty(tbl) then
		return nil
	end
	
	local keys = TableUtil.keys(tbl)
	local randomKey = keys[math.random(1, #keys)]
	return tbl[randomKey], randomKey
end

-- 打亂數組
function TableUtil.shuffle(tbl)
	if type(tbl) ~= "table" then
		return {}
	end
	
	local result = TableUtil.shallowCopy(tbl)
	for i = #result, 2, -1 do
		local j = math.random(i)
		result[i], result[j] = result[j], result[i]
	end
	
	return result
end

-- 分組
function TableUtil.groupBy(tbl, keySelector)
	if type(tbl) ~= "table" or type(keySelector) ~= "function" then
		return {}
	end
	
	local groups = {}
	for _, value in pairs(tbl) do
		local key = keySelector(value)
		if not groups[key] then
			groups[key] = {}
		end
		table.insert(groups[key], value)
	end
	
	return groups
end

-- 排序（返回新表格）
function TableUtil.sort(tbl, compareFn)
	if type(tbl) ~= "table" then
		return {}
	end
	
	local result = TableUtil.shallowCopy(tbl)
	table.sort(result, compareFn)
	return result
end

-- 去重
function TableUtil.unique(tbl)
	if type(tbl) ~= "table" then
		return {}
	end
	
	local seen = {}
	local result = {}
	
	for _, value in ipairs(tbl) do
		if not seen[value] then
			seen[value] = true
			table.insert(result, value)
		end
	end
	
	return result
end

-- 扁平化數組
function TableUtil.flatten(tbl, depth)
	if type(tbl) ~= "table" then
		return {}
	end
	
	depth = depth or math.huge
	local result = {}
	
	local function flattenHelper(arr, currentDepth)
		for _, value in ipairs(arr) do
			if type(value) == "table" and currentDepth > 0 then
				flattenHelper(value, currentDepth - 1)
			else
				table.insert(result, value)
			end
		end
	end
	
	flattenHelper(tbl, depth)
	return result
end

-- 分塊
function TableUtil.chunk(tbl, size)
	if type(tbl) ~= "table" or type(size) ~= "number" or size <= 0 then
		return {}
	end
	
	local result = {}
	local chunk = {}
	
	for i, value in ipairs(tbl) do
		table.insert(chunk, value)
		
		if #chunk == size or i == #tbl then
			table.insert(result, chunk)
			chunk = {}
		end
	end
	
	return result
end

return TableUtil
