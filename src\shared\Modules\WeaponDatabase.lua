--[[
	WeaponDatabase.lua - 武器資料庫
	包含所有武器的配置數據和特效綁定
]]

local WeaponDatabase = {}

-- 武器稀有度定義
WeaponDatabase.Rarities = {
	Common = {
		name = "普通",
		color = Color3.fromRGB(150, 150, 150),
		probability = 0.60,
		stars = 1,
	},
	Uncommon = {
		name = "稀有",
		color = Color3.fromRGB(0, 255, 0),
		probability = 0.25,
		stars = 2,
	},
	Rare = {
		name = "史詩",
		color = Color3.fromRGB(0, 100, 255),
		probability = 0.12,
		stars = 3,
	},
	Epic = {
		name = "傳說",
		color = Color3.fromRGB(128, 0, 255),
		probability = 0.025,
		stars = 4,
	},
	Legendary = {
		name = "神話",
		color = Color3.fromRGB(255, 215, 0),
		probability = 0.005,
		stars = 5,
	},
}

-- 武器配置數據
WeaponDatabase.Weapons = {
	-- 普通武器
	WoodenSword = {
		id = "WoodenSword",
		name = "木劍",
		rarity = "Common",
		weaponType = "Sword",
		description = "簡單的木製劍，適合初學者使用。",
		stats = {
			damage = 15,
			range = 8,
			attackSpeed = 1.2,
			criticalChance = 0.05,
			criticalMultiplier = 1.5,
		},
		appearance = {
			color = Color3.fromRGB(139, 69, 19),
			material = Enum.Material.Wood,
			size = Vector3.new(0.5, 0.2, 4),
		},
		effects = {
			swingEffect = "BasicSwing",
			hitEffect = "BasicHit",
			sound = "WoodSwing",
		},
		requirements = {
			level = 1,
		},
	},
	
	IronSword = {
		id = "IronSword",
		name = "鐵劍",
		rarity = "Common",
		weaponType = "Sword",
		description = "堅固的鐵製劍，比木劍更加鋒利。",
		stats = {
			damage = 25,
			range = 9,
			attackSpeed = 1.0,
			criticalChance = 0.08,
			criticalMultiplier = 1.6,
		},
		appearance = {
			color = Color3.fromRGB(105, 105, 105),
			material = Enum.Material.Metal,
			size = Vector3.new(0.4, 0.15, 4.5),
		},
		effects = {
			swingEffect = "MetalSwing",
			hitEffect = "MetalHit",
			sound = "MetalSwing",
		},
		requirements = {
			level = 5,
		},
	},
	
	-- 稀有武器
	SteelBlade = {
		id = "SteelBlade",
		name = "鋼刃",
		rarity = "Uncommon",
		weaponType = "Sword",
		description = "精鍛的鋼製刀刃，鋒利無比。",
		stats = {
			damage = 35,
			range = 10,
			attackSpeed = 0.9,
			criticalChance = 0.12,
			criticalMultiplier = 1.8,
		},
		appearance = {
			color = Color3.fromRGB(192, 192, 192),
			material = Enum.Material.Metal,
			size = Vector3.new(0.3, 0.1, 5),
		},
		effects = {
			swingEffect = "SteelSwing",
			hitEffect = "SteelHit",
			sound = "SteelSwing",
			trailEffect = "SteelTrail",
		},
		requirements = {
			level = 10,
		},
		specialAbilities = {
			"Sharp Edge", -- 鋒利邊緣：增加暴擊率
		},
	},
	
	FlameEdge = {
		id = "FlameEdge",
		name = "烈焰之刃",
		rarity = "Uncommon",
		weaponType = "Sword",
		description = "燃燒著永恆火焰的劍，攻擊帶有火焰傷害。",
		stats = {
			damage = 32,
			range = 10,
			attackSpeed = 1.0,
			criticalChance = 0.10,
			criticalMultiplier = 2.0,
			elementalDamage = {
				type = "Fire",
				damage = 8,
			},
		},
		appearance = {
			color = Color3.fromRGB(255, 100, 0),
			material = Enum.Material.Neon,
			size = Vector3.new(0.3, 0.1, 5),
		},
		effects = {
			swingEffect = "FlameSwing",
			hitEffect = "FlameExplosion",
			sound = "FlameSwing",
			trailEffect = "FlameTrail",
			ambientEffect = "FlameAura",
		},
		requirements = {
			level = 12,
		},
		specialAbilities = {
			"Burning Strike", -- 燃燒打擊：造成持續火焰傷害
		},
	},
	
	-- 史詩武器
	DragonFang = {
		id = "DragonFang",
		name = "龍牙劍",
		rarity = "Rare",
		weaponType = "Sword",
		description = "用古龍牙齒鍛造的傳奇武器，蘊含龍族力量。",
		stats = {
			damage = 50,
			range = 12,
			attackSpeed = 0.8,
			criticalChance = 0.18,
			criticalMultiplier = 2.5,
			elementalDamage = {
				type = "Dragon",
				damage = 15,
			},
		},
		appearance = {
			color = Color3.fromRGB(200, 0, 0),
			material = Enum.Material.Neon,
			size = Vector3.new(0.4, 0.15, 6),
		},
		effects = {
			swingEffect = "DragonSwing",
			hitEffect = "DragonBreath",
			sound = "DragonRoar",
			trailEffect = "DragonTrail",
			ambientEffect = "DragonAura",
		},
		requirements = {
			level = 20,
		},
		specialAbilities = {
			"Dragon's Wrath", -- 龍之怒：大幅增加攻擊力
			"Scale Armor", -- 龍鱗護甲：增加防禦力
		},
	},
	
	-- 傳說武器
	Excalibur = {
		id = "Excalibur",
		name = "王者之劍",
		rarity = "Epic",
		weaponType = "Sword",
		description = "傳說中的聖劍，只有真正的王者才能駕馭。",
		stats = {
			damage = 70,
			range = 15,
			attackSpeed = 0.7,
			criticalChance = 0.25,
			criticalMultiplier = 3.0,
			elementalDamage = {
				type = "Holy",
				damage = 20,
			},
		},
		appearance = {
			color = Color3.fromRGB(255, 215, 0),
			material = Enum.Material.Neon,
			size = Vector3.new(0.5, 0.2, 7),
		},
		effects = {
			swingEffect = "HolySwing",
			hitEffect = "HolyExplosion",
			sound = "HolyChime",
			trailEffect = "HolyTrail",
			ambientEffect = "HolyAura",
		},
		requirements = {
			level = 30,
		},
		specialAbilities = {
			"Divine Strike", -- 神聖打擊：對邪惡生物造成額外傷害
			"Blessing", -- 祝福：為周圍盟友提供增益
			"Light Beam", -- 光束：遠程攻擊能力
		},
	},
	
	-- 神話武器
	CosmicBlade = {
		id = "CosmicBlade",
		name = "宇宙之刃",
		rarity = "Legendary",
		weaponType = "Sword",
		description = "由星辰精華鍛造的終極武器，擁有操控時空的力量。",
		stats = {
			damage = 100,
			range = 20,
			attackSpeed = 0.5,
			criticalChance = 0.35,
			criticalMultiplier = 4.0,
			elementalDamage = {
				type = "Cosmic",
				damage = 30,
			},
		},
		appearance = {
			color = Color3.fromRGB(75, 0, 130),
			material = Enum.Material.ForceField,
			size = Vector3.new(0.6, 0.25, 8),
		},
		effects = {
			swingEffect = "CosmicSwing",
			hitEffect = "CosmicExplosion",
			sound = "CosmicHum",
			trailEffect = "CosmicTrail",
			ambientEffect = "CosmicAura",
		},
		requirements = {
			level = 50,
		},
		specialAbilities = {
			"Void Slash", -- 虛空斬：無視防禦
			"Time Freeze", -- 時間凍結：暫停敵人行動
			"Stellar Storm", -- 星辰風暴：範圍攻擊
			"Reality Rift", -- 現實裂隙：傳送攻擊
		},
	},
}

-- 獲取武器配置
function WeaponDatabase.getWeapon(weaponId)
	return WeaponDatabase.Weapons[weaponId]
end

-- 獲取所有武器
function WeaponDatabase.getAllWeapons()
	return WeaponDatabase.Weapons
end

-- 根據稀有度獲取武器列表
function WeaponDatabase.getWeaponsByRarity(rarity)
	local weapons = {}
	for weaponId, weaponData in pairs(WeaponDatabase.Weapons) do
		if weaponData.rarity == rarity then
			weapons[weaponId] = weaponData
		end
	end
	return weapons
end

-- 根據等級需求獲取可用武器
function WeaponDatabase.getAvailableWeapons(playerLevel)
	local weapons = {}
	for weaponId, weaponData in pairs(WeaponDatabase.Weapons) do
		if weaponData.requirements.level <= playerLevel then
			weapons[weaponId] = weaponData
		end
	end
	return weapons
end

-- 獲取稀有度信息
function WeaponDatabase.getRarity(rarityName)
	return WeaponDatabase.Rarities[rarityName]
end

return WeaponDatabase
